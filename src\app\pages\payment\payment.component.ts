// src/app/payment/payment.component.ts
import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { loadStripe, Stripe, StripeElements, StripeCardElement } from '@stripe/stripe-js';
import { HttpClient } from '@angular/common/http';
import { environment } from '../../../environments/environment';
import { ActivatedRoute } from '@angular/router';
import { catchError } from 'rxjs/operators';
import { of } from 'rxjs';
import { Reservation, StrapiSingleResponse } from '../../models/reservation.model';

@Component({
  selector: 'app-payment',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './payment.component.html',
  styleUrls: ['./payment.component.scss']
})
export class PaymentComponent implements OnInit {
  stripe: Stripe | null = null;
  elements: StripeElements | null = null;
  card: StripeCardElement | null = null;
  cardForm = { cardName: '' };
  reservationDetails: any = {};
  orderSummary: any = {};
  paymentError: string | null = null;
  paymentSuccess = false;
  securityFeatures = [
    { icon: 'lock', text: 'Paiement sécurisé SSL 256-bit' },
    { icon: 'verified_user', text: 'Données protégées et cryptées' },
    { icon: 'shield', text: 'Conforme aux normes PCI DSS' }
  ];

  constructor(private http: HttpClient, private route: ActivatedRoute) {}

  // Function to get document ID from reservation ID
  private async getReservationDocumentId(reservationId: number): Promise<string | null> {
    try {
      const response = await this.http.get<{
        data: Array<{
          id: number;
          documentId: string;
          booking_date: string;
          ticket_count: number;
          total_price: number;
          payment_reference: string | null;
          createdAt: string;
          updatedAt: string;
          publishedAt: string;
          statutR: string;
        }>;
        meta: {
          pagination: {
            page: number;
            pageSize: number;
            pageCount: number;
            total: number;
          };
        };
      }>(`${environment.apiUrl}/api/reservations`).pipe(
        catchError(error => {
          console.error('Error fetching reservations:', error);
          return of(null);
        })
      )
      .toPromise();

      if (response && response.data) {
        const reservation = response.data.find(r => r.id === reservationId);
        return reservation ? reservation.documentId : null;
      }
      return null;
    } catch (error) {
      console.error('Error fetching reservations:', error);
      return null;
    }
  }

  async ngOnInit() {
    // Initialize Stripe
    loadStripe(environment.stripePublishableKey).then(stripe => {
      this.stripe = stripe;
      if (stripe) {
        this.elements = stripe.elements();
        this.card = this.elements.create('card', {
          style: {
            base: { fontSize: '16px', color: '#32325d', '::placeholder': { color: '#aab7c4' } }
          }
        });
        this.card.mount('#card-element');
      }
    });

    // Fetch reservation details from Strapi
    const reservationIdtoGET = this.route.snapshot.paramMap.get('reservationId');
    console.log('Reservation ID:', reservationIdtoGET);

    if (!reservationIdtoGET) {
      this.paymentError = 'ID de réservation manquant.';
      return;
    }

    const reservationIdNumber = parseInt(reservationIdtoGET, 10);
    if (isNaN(reservationIdNumber)) {
      this.paymentError = 'ID de réservation invalide.';
      return;
    }

    const reservationId = await this.getReservationDocumentId(reservationIdNumber);
    if (reservationId) {
      this.http.get<StrapiSingleResponse>(`${environment.apiUrl}/api/reservations/${reservationId}?populate=*`)
        .pipe(
          catchError(error => {
            this.paymentError = 'Erreur lors du chargement de la réservation.';
            console.error('Error fetching reservation:', error);
            return of(null);
          })
        )
        .subscribe(response => {
          if (response?.data) {
            const reservation = response.data.attributes;
            this.reservationDetails = {
              id: reservation.id,
              eventTitle: reservation.eventId || 'N/A',
              eventDate: reservation.booking_date,
              eventTime: reservation.event_time || 'N/A',
              tableNumber: reservation.tableNumber || 'N/A',
              numberOfGuests: reservation.ticket_count,
              pricePerPerson: reservation.price_per_person || 85,
              reservationId: reservation.id || reservation.id,
              status: reservation.statutR || 'pending',
            };
            this.orderSummary = {
              subtotal: reservation.ticket_count * (reservation.price_per_person || 85),
              serviceFee: reservation.service_fee || 15,
              taxes: reservation.taxes || 25.50,
              total: reservation.total_price
            };
          } else {
            this.paymentError = 'Réservation introuvable.';
          }
        });
    } else {
      this.paymentError = 'ID de réservation manquant.';
    }
  }

  async processPayment() {
    if (!this.stripe) {
      this.paymentError = 'Erreur de configuration Stripe.';
      return;
    }

    if (!this.card) {
      this.paymentError = 'Erreur de configuration de la carte.';
      return;
    }

    if (!this.reservationDetails.id) {
      this.paymentError = 'Réservation non chargée.';
      return;
    }

    this.paymentError = null;
    try {
      // Create PaymentIntent
      const response = await this.http.post<{ clientSecret: string }>(
        `${environment.apiUrl}/api/reservations/create-payment-intent`,
        {
          reservationId: this.reservationDetails.id,
          amount: this.orderSummary.total,
          currency: 'tnd',
          eventTitle: this.reservationDetails.eventTitle,
          ticketCount: this.reservationDetails.numberOfGuests
        },
        { headers: { 'Idempotency-Key': this.reservationDetails.id } }
      ).toPromise();

      // Confirm PaymentIntent
      if (!response || !response.clientSecret) {
        this.paymentError = 'Erreur lors de la création de l\'intention de paiement.';
        return;
      }
      const result = await this.stripe.confirmCardPayment(response.clientSecret, {
        payment_method: {
          card: this.card,
          billing_details: { name: this.cardForm.cardName }
        }
      });

      if (result.error) {
        this.paymentError = result.error.message || 'Échec du paiement. Veuillez réessayer.';
      } else if (result.paymentIntent.status === 'succeeded') {
        // Update reservation status to confirmed
        await this.http.put(
          `${environment.apiUrl}/api/reservations/${this.reservationDetails.id}`,
          { data: { status: 'confirmed' } }
        ).toPromise();
        this.paymentSuccess = true;
      } else if (result.paymentIntent.status === 'requires_action') {
        if (result.paymentIntent.client_secret) {
          const actionResult = await this.stripe.handleCardAction(result.paymentIntent.client_secret);
        
          if (actionResult.error) {
            this.paymentError = actionResult.error.message || 'Échec de l\'authentification.';
          } else if (actionResult.paymentIntent.status === 'succeeded') {
            // Update reservation status to confirmed
            await this.http.put(
              `${environment.apiUrl}/api/reservations/${this.reservationDetails.id}`,
              { data: { status: 'confirmed' } }
            ).toPromise();
            this.paymentSuccess = true;
          }
        } else {
          throw new Error('Client secret is null.');
        }
      }
    } catch (error) {
      this.paymentError = 'Une erreur s\'est produite. Veuillez réessayer plus tard.';
      console.error('Payment error:', error);
    }
  }
}